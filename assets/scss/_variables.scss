// ===================================
// RESTAURANT RINDU KAMPUNG VARIABLES
// ===================================

// Color Palette - Green & Black Theme
// ===================================

// Primary Colors
$primary-green: #2D5016;        // Dark Forest Green
$primary-green-light: #4A7C59;  // Medium Green
$primary-green-lighter: #6B8E5A; // Light Green
$primary-green-dark: #1A3009;   // Very Dark Green

// Secondary Colors
$secondary-black: #1A1A1A;      // Pure Black
$secondary-charcoal: #2C2C2C;   // Charcoal
$secondary-gray: #4A4A4A;       // Dark Gray
$secondary-gray-light: #6B6B6B; // Medium Gray

// Accent Colors
$accent-gold: #D4AF37;          // Gold (for special highlights)
$accent-red: #DC143C;           // Crimson (for alerts/special items)
$accent-orange: #FF8C00;        // Dark Orange (for CTAs)

// Text Colors
$text-primary: $secondary-black;
$text-secondary: $secondary-gray;
$text-light: $secondary-gray-light;
$text-white: #FFFFFF;
$text-muted: #8A8A8A;

// Background Colors
$bg-primary: #FFFFFF;
$bg-secondary: #F8F9FA;
$bg-light: #F5F7F5;            // Very light green tint
$bg-dark: $secondary-black;
$bg-green: $primary-green;
$bg-green-light: rgba($primary-green-light, 0.1);
$bg-green-dark: rgba($primary-green-dark, 0.9);

// Border Colors
$border-light: #E5E7E5;
$border-medium: #D1D5D1;
$border-dark: $secondary-gray;
$border-green: $primary-green-light;

// Shadow Colors
$shadow-light: rgba(0, 0, 0, 0.1);
$shadow-medium: rgba(0, 0, 0, 0.15);
$shadow-dark: rgba(0, 0, 0, 0.25);
$shadow-green: rgba($primary-green, 0.2);

// Typography
// ===================================

// Font Families
$font-primary: 'Inter', sans-serif;
$font-heading: 'Playfair Display', serif;

// Font Weights
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Font Sizes
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px
$font-size-5xl: 3rem;      // 48px

// Line Heights
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.6;
$line-height-loose: 1.8;

// Spacing
// ===================================

$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-2xl: 3rem;     // 48px
$spacing-3xl: 4rem;     // 64px
$spacing-4xl: 5rem;     // 80px

// Border Radius
// ===================================

$radius-sm: 4px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 16px;
$radius-2xl: 20px;
$radius-full: 50%;

// Breakpoints
// ===================================

$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-Index
// ===================================

$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// Transitions
// ===================================

$transition-fast: 0.15s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// Common transition properties
$transition-all: all $transition-normal;
$transition-colors: color $transition-normal, background-color $transition-normal, border-color $transition-normal;
$transition-transform: transform $transition-normal;
$transition-opacity: opacity $transition-normal;

// Component Specific Variables
// ===================================

// Buttons
$btn-padding-sm: $spacing-sm $spacing-md;
$btn-padding-md: $spacing-md $spacing-xl;
$btn-padding-lg: $spacing-lg ($spacing-xl + $spacing-sm);

// Cards
$card-padding: $spacing-xl;
$card-radius: $radius-lg;
$card-shadow: 0 4px 20px $shadow-light;
$card-shadow-hover: 0 8px 30px $shadow-medium;

// Navigation
$navbar-height: 80px;
$navbar-padding: $spacing-md 0;

// Sections
$section-padding: $spacing-3xl 0;
$section-padding-sm: $spacing-2xl 0;

// Container
$container-max-width: 1200px;
$container-padding: 0 $spacing-md;

// Grid
$grid-gap: $spacing-xl;
$grid-gap-sm: $spacing-lg;

// Malaysian-Indonesian Cultural Colors (for special elements)
// ===================================

$cultural-batik-brown: #8B4513;
$cultural-batik-gold: #DAA520;
$cultural-tropical-green: #228B22;
$cultural-ocean-blue: #4682B4;
$cultural-sunset-orange: #FF6347;

// Status Colors
// ===================================

$success-color: $primary-green;
$warning-color: $accent-orange;
$error-color: $accent-red;
$info-color: #17A2B8;

// Utility Classes Variables
// ===================================

$utilities: (
  'margin': $spacing-md,
  'padding': $spacing-md,
  'gap': $spacing-md,
  'border-radius': $radius-md,
);
