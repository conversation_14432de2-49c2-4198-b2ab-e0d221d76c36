<template>
  <section id="menu" class="section menu-preview">
    <div class="container">
      <h2 class="section-title">Our Signature Dishes</h2>
      <p class="section-subtitle">
        Discover the authentic flavors of Malaysia and Indonesia
      </p>

      <div class="menu-categories">
        <button 
          v-for="category in categories" 
          :key="category.id"
          class="category-btn"
          :class="{ 'active': activeCategory === category.id }"
          @click="setActiveCategory(category.id)"
        >
          {{ category.name }}
        </button>
      </div>

      <div class="menu-items grid grid-3">
        <div 
          v-for="item in filteredItems" 
          :key="item.id"
          class="menu-item card"
        >
          <div class="menu-item-image">
            <div class="image-placeholder">
              <span class="cuisine-flag">{{ item.flag }}</span>
            </div>
            <div class="item-badge" v-if="item.isSpecial">Special</div>
          </div>
          
          <div class="menu-item-content">
            <h3 class="item-name">{{ item.name }}</h3>
            <p class="item-description">{{ item.description }}</p>
            <div class="item-footer">
              <span class="item-price">${{ item.price }}</span>
              <span class="item-origin">{{ item.origin }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="menu-cta">
        <a href="#contact" class="btn btn-primary btn-large">View Full Menu</a>
        <a href="tel:+1234567890" class="btn btn-secondary btn-large">Order Now</a>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface MenuItem {
  id: number
  name: string
  description: string
  price: number
  category: string
  origin: string
  flag: string
  isSpecial?: boolean
}

interface Category {
  id: string
  name: string
}

const activeCategory = ref('all')

const categories: Category[] = [
  { id: 'all', name: 'All Dishes' },
  { id: 'appetizers', name: 'Appetizers' },
  { id: 'mains', name: 'Main Courses' },
  { id: 'desserts', name: 'Desserts' }
]

const menuItems: MenuItem[] = [
  {
    id: 1,
    name: 'Nasi Lemak',
    description: 'Fragrant coconut rice served with sambal, anchovies, peanuts, and boiled egg',
    price: 18.90,
    category: 'mains',
    origin: 'Malaysian',
    flag: '🇲🇾',
    isSpecial: true
  },
  {
    id: 2,
    name: 'Rendang Beef',
    description: 'Slow-cooked beef in rich coconut curry with aromatic spices',
    price: 24.90,
    category: 'mains',
    origin: 'Indonesian',
    flag: '🇮🇩',
    isSpecial: true
  },
  {
    id: 3,
    name: 'Satay Skewers',
    description: 'Grilled marinated chicken skewers with peanut sauce and cucumber salad',
    price: 16.90,
    category: 'appetizers',
    origin: 'Malaysian',
    flag: '🇲🇾'
  },
  {
    id: 4,
    name: 'Gado-Gado',
    description: 'Indonesian salad with mixed vegetables, tofu, and peanut dressing',
    price: 15.90,
    category: 'appetizers',
    origin: 'Indonesian',
    flag: '🇮🇩'
  },
  {
    id: 5,
    name: 'Char Kway Teow',
    description: 'Stir-fried rice noodles with prawns, Chinese sausage, and bean sprouts',
    price: 19.90,
    category: 'mains',
    origin: 'Malaysian',
    flag: '🇲🇾'
  },
  {
    id: 6,
    name: 'Cendol',
    description: 'Traditional dessert with pandan jelly, coconut milk, and palm sugar',
    price: 8.90,
    category: 'desserts',
    origin: 'Malaysian',
    flag: '🇲🇾'
  }
]

const filteredItems = computed(() => {
  if (activeCategory.value === 'all') {
    return menuItems
  }
  return menuItems.filter(item => item.category === activeCategory.value)
})

const setActiveCategory = (categoryId: string) => {
  activeCategory.value = categoryId
}
</script>

<style scoped>
.menu-preview {
  background: var(--background-light);
}

.section-subtitle {
  text-align: center;
  color: var(--text-light);
  font-size: 1.125rem;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.menu-categories {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.category-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--border-color);
  background: white;
  color: var(--text-light);
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-btn:hover,
.category-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.menu-item {
  overflow: hidden;
  transition: transform 0.3s ease;
}

.menu-item:hover {
  transform: translateY(-5px);
}

.menu-item-image {
  position: relative;
  height: 200px;
  margin-bottom: 1.5rem;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  background-image: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.cuisine-flag {
  font-size: 3rem;
  z-index: 1;
  position: relative;
}

.item-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--accent-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
}

.menu-item-content {
  padding: 0 0.5rem;
}

.item-name {
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.item-description {
  color: var(--text-light);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
}

.item-origin {
  font-size: 0.875rem;
  color: var(--text-light);
  font-style: italic;
}

.menu-cta {
  text-align: center;
  margin-top: 3rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive design */
@media (max-width: 768px) {
  .menu-categories {
    gap: 0.5rem;
  }

  .category-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .menu-cta {
    flex-direction: column;
    align-items: center;
  }

  .btn-large {
    width: 100%;
    max-width: 250px;
  }
}
</style>
