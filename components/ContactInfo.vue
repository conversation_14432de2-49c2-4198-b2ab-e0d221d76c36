<template>
  <section id="contact" class="section contact-section">
    <div class="container">
      <h2 class="section-title">Get in Touch</h2>
      <p class="section-subtitle">
        We'd love to hear from you. Reach out for reservations, catering, or any questions.
      </p>

      <div class="contact-content grid grid-2">
        <!-- Contact Information -->
        <div class="contact-info">
          <h3 class="contact-title">Contact Information</h3>
          
          <div class="contact-methods">
            <div class="contact-method">
              <div class="method-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
              </div>
              <div class="method-content">
                <h4>Phone</h4>
                <p>(*************</p>
                <span class="method-note">Call for reservations & takeout</span>
              </div>
            </div>

            <div class="contact-method">
              <div class="method-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                  <polyline points="22,6 12,13 2,6"/>
                </svg>
              </div>
              <div class="method-content">
                <h4>Email</h4>
                <p><EMAIL></p>
                <span class="method-note">General inquiries & catering</span>
              </div>
            </div>

            <div class="contact-method">
              <div class="method-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
              </div>
              <div class="method-content">
                <h4>Address</h4>
                <p>123 Heritage Street<br>Little Malaysia District<br>City Center, State 12345</p>
                <span class="method-note">Free parking available</span>
              </div>
            </div>

            <div class="contact-method">
              <div class="method-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10"/>
                  <polyline points="12,6 12,12 16,14"/>
                </svg>
              </div>
              <div class="method-content">
                <h4>Hours</h4>
                <p>Mon-Thu: 11AM-10PM<br>Fri-Sat: 11AM-11PM<br>Sun: 10AM-10PM</p>
                <span class="method-note">Last order 30 min before closing</span>
              </div>
            </div>
          </div>

          <!-- Social Media -->
          <div class="social-media">
            <h4>Follow Us</h4>
            <div class="social-links">
              <a href="#" class="social-link" aria-label="Facebook">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="#" class="social-link" aria-label="Instagram">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.596-3.205-1.533l1.714-1.714c.39.39.927.633 1.491.633.564 0 1.101-.243 1.491-.633.39-.39.633-.927.633-1.491 0-.564-.243-1.101-.633-1.491-.39-.39-.927-.633-1.491-.633-.564 0-1.101.243-1.491.633l-1.714-1.714c.757-.937 1.908-1.533 3.205-1.533 2.347 0 4.25 1.903 4.25 4.25s-1.903 4.25-4.25 4.25zm7.718-2.718c-.39.39-.927.633-1.491.633-.564 0-1.101-.243-1.491-.633-.39-.39-.633-.927-.633-1.491 0-.564.243-1.101.633-1.491.39-.39.927-.633 1.491-.633.564 0 1.101.243 1.491.633l1.714-1.714c-.757-.937-1.908-1.533-3.205-1.533-2.347 0-4.25 1.903-4.25 4.25s1.903 4.25 4.25 4.25c1.297 0 2.448-.596 3.205-1.533l-1.714-1.714z"/>
                </svg>
              </a>
              <a href="#" class="social-link" aria-label="TikTok">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="contact-form-container">
          <h3 class="form-title">Send us a Message</h3>
          <form @submit.prevent="submitForm" class="contact-form">
            <div class="form-group">
              <label for="name">Full Name *</label>
              <input 
                type="text" 
                id="name" 
                v-model="form.name" 
                required 
                placeholder="Your full name"
              >
            </div>

            <div class="form-group">
              <label for="email">Email Address *</label>
              <input 
                type="email" 
                id="email" 
                v-model="form.email" 
                required 
                placeholder="<EMAIL>"
              >
            </div>

            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input 
                type="tel" 
                id="phone" 
                v-model="form.phone" 
                placeholder="(*************"
              >
            </div>

            <div class="form-group">
              <label for="subject">Subject *</label>
              <select id="subject" v-model="form.subject" required>
                <option value="">Select a subject</option>
                <option value="reservation">Table Reservation</option>
                <option value="catering">Catering Inquiry</option>
                <option value="feedback">Feedback</option>
                <option value="general">General Question</option>
              </select>
            </div>

            <div class="form-group">
              <label for="message">Message *</label>
              <textarea 
                id="message" 
                v-model="form.message" 
                required 
                rows="5"
                placeholder="Tell us how we can help you..."
              ></textarea>
            </div>

            <button type="submit" class="btn btn-primary btn-large" :disabled="isSubmitting">
              <span v-if="!isSubmitting">Send Message</span>
              <span v-else>Sending...</span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const isSubmitting = ref(false)

const form = reactive({
  name: '',
  email: '',
  phone: '',
  subject: '',
  message: ''
})

const submitForm = async () => {
  isSubmitting.value = true
  
  try {
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // In a real app, you would send this to your backend
    console.log('Form submitted:', form)
    
    // Reset form
    Object.assign(form, {
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    })
    
    alert('Thank you for your message! We\'ll get back to you soon.')
  } catch (error) {
    console.error('Error submitting form:', error)
    alert('Sorry, there was an error sending your message. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
.contact-section {
  background: var(--background-light);
}

.section-subtitle {
  text-align: center;
  color: var(--text-light);
  font-size: 1.125rem;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.contact-content {
  gap: 4rem;
}

.contact-title,
.form-title {
  color: var(--text-dark);
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 3rem;
}

.contact-method {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.method-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.method-icon svg {
  width: 24px;
  height: 24px;
  color: white;
  stroke-width: 2;
}

.method-content h4 {
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.method-content p {
  color: var(--text-light);
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.method-note {
  font-size: 0.875rem;
  color: var(--text-light);
  font-style: italic;
}

.social-media h4 {
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
}

.social-link svg {
  width: 20px;
  height: 20px;
}

.contact-form-container {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-dark);
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  width: 100%;
}

/* Responsive design */
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-form-container {
    padding: 1.5rem;
  }

  .contact-method {
    gap: 1rem;
  }

  .method-icon {
    width: 40px;
    height: 40px;
  }

  .method-icon svg {
    width: 20px;
    height: 20px;
  }
}
</style>
