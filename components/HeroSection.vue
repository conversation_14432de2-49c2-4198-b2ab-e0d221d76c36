<template>
  <section id="home" class="hero">
    <div class="hero-background">
      <div class="hero-overlay"></div>
    </div>
    
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title fade-in-up">
            Restaurant <span class="text-primary">Rindu Kampung</span>
          </h1>
          <p class="hero-motto fade-in-up">Land below the wind</p>
          <p class="hero-description fade-in-up">
            Experience the authentic flavors of Malaysian-Indonesian cuisine in a warm, 
            welcoming atmosphere that brings the taste of home to your table.
          </p>
          <div class="hero-actions fade-in-up">
            <a href="#menu" class="btn btn-primary btn-large">View Menu</a>
            <a href="tel:+1234567890" class="btn btn-secondary btn-large">Make Reservation</a>
          </div>
        </div>
        
        <div class="hero-image">
          <div class="hero-image-placeholder">
            <div class="image-content">
              <span class="image-text">Delicious Malaysian-Indonesian Cuisine</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scroll indicator -->
    <div class="scroll-indicator">
      <div class="scroll-arrow"></div>
    </div>
  </section>
</template>

<script setup lang="ts">
// No reactive data needed for this component
</script>

<style scoped>
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  padding-top: 80px; /* Account for fixed navbar */
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--background-light) 0%, #FFF5E6 100%);
  z-index: -2;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
  z-index: -1;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  align-items: center;
  min-height: calc(100vh - 160px);
}

.hero-text {
  text-align: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
  line-height: 1.1;
}

.hero-motto {
  font-size: 1.5rem;
  color: var(--text-light);
  font-style: italic;
  margin-bottom: 1.5rem;
  font-family: 'Playfair Display', serif;
}

.hero-description {
  font-size: 1.125rem;
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto 2.5rem;
  line-height: 1.7;
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  min-width: 200px;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image-placeholder {
  width: 100%;
  max-width: 500px;
  height: 400px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.hero-image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.image-content {
  text-align: center;
  color: white;
  z-index: 1;
  position: relative;
}

.image-text {
  font-size: 1.25rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 24px;
  height: 24px;
  border-right: 2px solid var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  transform: rotate(45deg);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Responsive design */
@media (min-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }

  .hero-text {
    text-align: left;
  }

  .hero-actions {
    flex-direction: row;
    justify-content: flex-start;
  }

  .hero-title {
    font-size: 3.5rem;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 4rem;
  }

  .hero-description {
    font-size: 1.25rem;
  }
}

@media (max-width: 767px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-motto {
    font-size: 1.25rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .btn-large {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
    min-width: 180px;
  }

  .hero-image-placeholder {
    height: 300px;
  }
}
</style>
